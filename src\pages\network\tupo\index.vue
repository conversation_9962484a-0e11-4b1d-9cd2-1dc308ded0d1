<script lang="ts" setup>
import * as d3 from 'd3'
import jsPDF from 'jspdf'
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import icon2G from '@images/pages/2.4g-legend.png'
import icon5G from '@images/pages/5g-legend.png'
import clinetIcon from '@images/pages/icon-pc.png'
import InternetPng from '@images/pages/internet.png'

import apIcon from '@images/pages/ap-icon.png'
import topologyIcon from '@images/pages/topology-icon.png'

const { t } = useI18n()

const fakeData = ref<any>({})
let root: any = null

interface NodePosition {
  x: number
  y: number
}
const svgEle = ref(null) as Ref<HTMLElement | null>
const outerContainer = ref(null) as Ref<HTMLElement | null>

const convertACToTreeData = (info: any) => {
  const acData = info.ac
  const apData = info.ap
  if (apData.length == 0) {
    return {
      name: acData.name,
      type: 'AC',
      children: (acData.ap || []).map((ap: any) => ({
        name: ap.model,
        children: (ap.clients || []).map((client: any) => ({
          name: client.name || client.mac,
          id: client.mac,
          type: t('Network.Tupo.Terminal'),
          ...client,
        })),
        type: 'AP',
        ...ap,
      })),
      ...acData,
    }
  }
  else {
    const dataInfo = {
      name: t('Network.Tupo.Internet'),
      type: 'internet',
      children: [
        {
          name: acData.name,
          type: 'AC',
          children: (acData.ap || []).map((ap: any) => ({
            name: ap.model,
            children: (ap.clients || []).map((client: any) => ({
              name: client.name || client.mac,
              id: client.mac,
              type: t('Network.Tupo.Terminal'),
              ...client,
            })),
            type: 'AP',
            ...ap,
          })),
          ...acData,
        },
      ],
    }

    const list = (apData || []).map((ap: any) => ({
      name: ap.model,
      children: (ap.clients || []).map((client: any) => ({
        name: client.name || client.mac,
        id: client.mac,
        type: t('Network.Tupo.Terminal'),
        ...client,
      })),
      type: 'AP',
      ...ap,
    }))

    dataInfo.children = dataInfo.children.concat(list)

    return dataInfo
  }
}

watch(fakeData, val => {
  if (val) {
    try {
      root = d3.hierarchy(val)
      init()
    }
    catch (e) {
    }
  }
})

const getTupoList = () => {
  $api('', { requestType: 543 }).then(res => {
    if (res.err_code === 0)
      fakeData.value = convertACToTreeData(res.info)

    // init() 由 watch 自动触发
    getAPMoreList()
  })
}

const timer = ref<NodeJS.Timeout | null>(null)

// 递归遍历树结构，查找所有type为AP的节点
const findAllAPNodes = (node: any): any[] => {
  let apNodes: any[] = []

  if (node.type === 'AP')
    apNodes.push(node)

  if (node.children && Array.isArray(node.children)) {
    node.children.forEach((child: any) => {
      apNodes = apNodes.concat(findAllAPNodes(child))
    })
  }

  return apNodes
}

// 递归更新树结构中的AP节点
const updateAPNodeInTree = (node: any, updatedAP: any): boolean => {
  if (node.type === 'AP' && node.sn === updatedAP.sn) {
    // 更新节点数据，保留原有的children
    Object.assign(node, updatedAP, { children: node.children })

    return true
  }

  if (node.children && Array.isArray(node.children)) {
    for (const child of node.children) {
      if (updateAPNodeInTree(child, updatedAP))
        return true
    }
  }

  return false
}

const getAPMoreList = () => {
  if (timer.value) {
    clearTimeout(timer.value)
    timer.value = null
  }
  timer.value = setTimeout(() => {
    $api('', {
      requestType: 506,
    }).then(res => {
      if (res.err_code === 0 && res.info && res.info.aplist) {
        const newAPList = res.info.aplist

        // 获取当前fakeData中所有的AP节点
        const currentAPNodes = findAllAPNodes(fakeData.value)
        const currentAPSNs = new Set(currentAPNodes.map((ap: any) => ap.sn).filter(Boolean))
        const newAPSNs = new Set(newAPList.map((ap: any) => ap.sn).filter(Boolean))

        // 检测AP数量是否一致
        if (currentAPSNs.size !== newAPSNs.size) {
          console.log('检测到AP数量不一致，刷新页面:', {
            current: currentAPSNs.size,
            new: newAPSNs.size,
          })
          window.location.reload()

          return
        }

        // 检测AP的sn是否一致（任何一个sn不匹配就刷新）
        const hasInconsistentSN = Array.from(currentAPSNs).some(sn => !newAPSNs.has(sn))
                                  || Array.from(newAPSNs).some(sn => !currentAPSNs.has(sn))

        if (hasInconsistentSN) {
          console.log('检测到AP的sn不一致，刷新页面:', {
            currentSNs: Array.from(currentAPSNs),
            newSNs: Array.from(newAPSNs),
          })
          window.location.reload()

          return
        }

        // 根据sn匹配并更新AP数据
        newAPList.forEach((newAP: any) => {
          if (newAP.sn)
            updateAPNodeInTree(fakeData.value, newAP)
        })

        console.log('更新后的fakeData:', fakeData.value)
      }

      getAPMoreList()
    }).catch(error => {
      console.error('getAPMoreList API调用失败:', error)
      getAPMoreList()
    })
  }, 30000)
}

const nodeItemWidth = 42
const nodeItemHeight = 42
const expanderWidth = 16
const expanderHeight = 16
let rootLink: any = null
let myRootLinks: any = null
let rootNode: any = null
let activeNode: any = null

const radius = 10
const dx = 90
const dy = 170
const treeStructuring = d3.tree().nodeSize([dx, dy])

const svg = d3.create('svg')
  .attr('id', 'svg-ele')
  .attr('style', 'max-width: 100%; height: auto;')
  .attr('font-family', 'sans-serif')
  .attr('font-size', 10)
  .attr('xmlns', 'http://www.w3.org/2000/svg')
  .attr('xmlns:xlink', 'http://www.w3.org/1999/xlink')

function drawRoundedPath({ source, target }: Record<string, NodePosition>) {
  const path = d3.path()
  const midX = (source.x + target.x) / 2

  path.moveTo(midX, source.y)
  if (source.y === target.y) {
    path.lineTo(target.x, target.y)
  }
  else {
    if (source.y < target.y) {
      // 下方
      path.lineTo(midX, target.y - radius)
    }
    else {
      // 上方
      path.lineTo(midX, target.y + radius)
    }

    path.arcTo(midX, target.y, target.x, target.y, radius)

    path.lineTo(target.x, target.y)
  }

  return path.toString()
};

function drawExtendPath({ source, target }: Record<string, NodePosition>) {
  const path = d3.path()

  path.moveTo(source.x, source.y)

  const midX = (source.x + target.x) / 2

  path.lineTo(midX, source.y)

  return path.toString()
};

function update(data: any) {
  const transition = svg
    .transition()
    .duration(250)

  // .tween('resize', window.ResizeObserver ? null : () => () => svg.dispatch('toggle'))
  treeStructuring(data)

  const allLinks = data.links()
  const existedId = new Set()
  const afterParentLinks: any[] = []

  // 收集所有符合要求的父节点
  allLinks.forEach((item: any) => {
    if (item.source.data.child_num > 0) {
      if (!existedId.has(item.source.id)) {
        existedId.add(item.source.id)
        afterParentLinks.push(item)
      }
    }
  })

  // 校验一下source.id是否存在
  const renderLinks = afterParentLinks.filter((item: any) => {
    return data.descendants().some((d: any) => d.id === item.source.id)
  })

  // 有子节点的父节点后面的连线
  const extendLinks = myRootLinks.selectAll('path').data(renderLinks, (d: any) => d.source.id)

  extendLinks.enter()
    .append('path')
    .style('opacity', 0)
    .transition(transition)
    .style('opacity', 1)
    .attr('d', (d: any) => {
      return drawExtendPath({
        source: {
          x: d.source.y,
          y: d.source.x,
        },
        target: {
          x: d.target.y,
          y: d.target.x,
        },
      })
    }).attr('stroke-dasharray', (d: any) => {
      if (d.target.depth === 3)
        return '4,2'

      return null
    })

  extendLinks
    .transition(transition)
    .attr('d', (d: any) => {
      return drawExtendPath({
        source: {
          x: d.source.y,
          y: d.source.x,
        },
        target: {
          x: d.target.y,
          y: d.target.x,
        },
      })
    })

  extendLinks.exit()
    .transition(transition)
    .style('opacity', 0)
    .remove()

  // 连线
  const links = rootLink
    .selectAll('path')
    .data(data.links(), (d: any) => d.target.id)

  const enterLinks = links
    .enter()
    .append('path')
    .attr('class', 'link-path')
    .style('opacity', 0)
    .attr('d', (d: any) => {
      if (d.target.isPlaceholder)
        return null

      return drawRoundedPath({
        source: {
          x: d.source.y,
          y: d.source.x,
        },
        target: {
          x: d.target.y,
          y: d.target.x,
        },
      })
    })
    .attr('stroke-dasharray', (d: any) => {
      if (d.target.depth === 3)
        return '4,2'

      return null
    })

  enterLinks.transition(transition)
    .style('opacity', 1)

  links
    .merge(enterLinks)
    .attr('d', (d: any) => {
      if (d.target.isPlaceholder)
        return null

      return drawRoundedPath({
        source: {
          x: d.source.y,
          y: d.source.x,
        },
        target: {
          x: d.target.y,
          y: d.target.x,
        },
      })
    })

  links
    .exit()
    .transition(transition)
    .style('opacity', 0)
    .remove()

  const nodes = rootNode.selectAll('g')
    .data(data.descendants(), (d: any) => d.id)

  const nodeItem = nodes
    .enter()
    .append('g')
    .attr('x', (d: any) => d.y)
    .attr('y', (d: any) => d.x)
    .attr('class', 'card')
    .attr('fill', '#f00')
    .attr('opacity', 0)

  nodeItem
    .transition(transition)
    .attr('opacity', 1)

  // 图标
  nodeItem
    .append('image')
    .attr('class', 'node-item')
    .attr('width', nodeItemWidth)
    .attr('height', nodeItemHeight)
    .attr('xlink:href', (d: any) => {
      if (d.data.type === 'AP')
        return apIcon
      else if (d.data.type === '终端')
        return clinetIcon
      else if (d.data.type === 'internet')
        return InternetPng
      else
        return topologyIcon
    })
    .attr('crossorigin', 'anonymous')
    .attr('x', (d: any) => d.y - nodeItemWidth / 2)
    .attr('y', (d: any) => d.x - nodeItemHeight / 2)
    .style('cursor', 'pointer')
    .attr('display', (d: any) => {
      if (d.data.isPlaceholder)
        return 'none'
    })
    .on('click', (e: Event, d: any) => {
      e.stopPropagation()

      // 如果是 internet 类型的节点，不显示弹窗
      if (d.data.type === 'internet')
        return

      if (activeNode === d.id)
        return
      svg.selectAll('.popover').remove()

      const popover = svg.append('g').attr('class', 'popover')

      activeNode = d.id
      update(root)
      popover
        .append('foreignObject')
        .attr('class', 'node-popover')
        .attr('width', 180)
        .attr('height', 76)
        .attr('style', 'padding-left: 10px;overflow: visible;')
        .attr('x', () => {
          return d.y + (nodeItemWidth / 2) + 10
        })
        .attr('y', () => {
          return d.x - (nodeItemHeight / 2)
        })
        .attr('transform', svgTransform as unknown as string)
        .html(() => {
          return `
                  <div class="node-popover-box pa-2 rounded">
                    <div class="item-title mb-2">${d.data.name}</div>
                    <div class="info-label">${t('Network.Tupo.Type')}</div>
                    <div class="info-content mb-2">${d.data.type || t('Network.Tupo.NotAvailable')}</div>
                    <div class="info-label">${t('Network.Tupo.IPAddress')}</div>
                    <div class="info-content mb-2">${d.data.ip}</div>
                    <div class="info-label">${t('Network.Tupo.MACAddress')}</div>
                    <div class="info-content mb-2">${d.data.mac}</div>
                    <div class="info-label">${t('Network.Tupo.RunningTime')}</div>
                    <div class="info-content">${d.data.uptime || d.data.assoc_time || '--'}</div>
                  </div>
                  `
        })
        .style('opacity', 0)
        .transition()
        .duration(200)
        .style('opacity', 1)
    })

  nodes
    .selectAll('.node-item')
    .transition(transition)
    .attr('x', (d: any) => d.y - nodeItemWidth / 2)
    .attr('y', (d: any) => d.x - nodeItemHeight / 2)
    .attr('display', (d: any) => {
      if (d.data.isPlaceholder)
        return 'none'
    })
    .attr('xlink:href', (d: any) => {
      if (d.data.type === 'AP')
        return apIcon
      else if (d.data.type === '终端')
        return clinetIcon
      else if (d.data.type === 'internet')
        return InternetPng
      else
        return topologyIcon
    })

  // 文字
  nodeItem
    .append('text')
    .attr('class', 'node-item-text')
    .attr('text-anchor', 'middle')
    .attr('font-size', '13px')
    .attr('fill', 'rgba(47, 43, 61, 0.7)')
    .attr('font-weight', 500)
    .text((d: any) => d.data.name)
    .attr('x', (d: any) => d.y)
    .attr('y', (d: any) => d.x + nodeItemHeight / 2 + 15)
    .attr('display', (d: any) => {
      if (d.data.isPlaceholder)
        return 'none'
    })

  nodes
    .selectAll('.node-item-text')
    .transition(transition)
    .attr('x', (d: any) => d.y)
    .attr('y', (d: any) => d.x + nodeItemHeight / 2 + 15)
    .attr('display', (d: any) => {
      if (d.data.isPlaceholder)
        return 'none'
    })

  nodes
    .exit()
    .transition(transition)
    .attr('opacity', 0)
    .remove()

  // 收起/展开
  nodeItem.append('rect')
    .attr('class', 'expander')
    .attr('x', (d: any) => d.y + (170 / 2))
    .attr('y', (d: any) => d.x)
    .attr('width', 25)
    .attr('height', 16)
    .attr('rx', 8)
    .attr('fill', '#4080FF')
    .style('transform', 'translate(-12.5px, -8px)')
    .attr('cursor', 'pointer')
    .attr('display', (d: any) => {
      if (!d.data.child_num)
        return 'none'

      if (!d.show_child)
        return 'block'
      else
        return 'none'
    })

  nodes.selectAll('.expander').attr('display', (d: any) => {
    if (!d.data.child_num)
      return 'none'

    if (!d.show_child)
      return 'block'
    else
      return 'none'
  })
    .transition(transition)
    .attr('x', (d: any) => d.y + (170 / 2))
    .attr('y', (d: any) => d.x)

  nodeItem.append('circle')
    .attr('class', 'pack-up')
    .attr('cx', (d: any) => d.y + (170 / 2))
    .attr('cy', (d: any) => d.x)
    .attr('r', expanderHeight / 2)
    .attr('fill', '#4080FF')
    .attr('display', (d: any) => {
      if (!d.data.child_num)
        return 'none'

      if (d.show_child)
        return 'block'
      else
        return 'none'
    })

  nodes.selectAll('.pack-up').attr('display', (d: any) => {
    if (!d.data.child_num)
      return 'none'

    if (d.show_child)
      return 'block'
    else
      return 'none'
  })
    .transition(transition)
    .attr('cx', (d: any) => d.y + (170 / 2))
    .attr('cy', (d: any) => d.x)

  nodeItem
    .append('text')
    .attr('class', 'point-text').text((d: any) => {
      if (d.show_child)
        return '-'
      else
        return d.data.child_num
    })
    .attr('x', (d: any) => d.y + (170 / 2))
    .attr('y', (d: any) => d.x).attr('text-anchor', 'middle')
    .attr('dy', '3px')
    .attr('fill', '#fff')
    .attr('font-size', (d: any) => {
      if (d.show_child)
        return '12px'
      else
        return '10px'
    })
    .attr('cursor', 'pointer')
    .attr('display', (d: any) => {
      if (d.data.child_num > 0)
        return 'block'

      return 'none'
    })
    .on('click', (e: Event, d: any) => {
      e.stopPropagation()
      if (d.show_child) {
        const placeholderNode = {
          id: `placeholder-${d.id}`,
          data: {
            name: 'placeholder',
            _children: [],
            child_num: 0,
            isPlaceholder: true,
          },
          depth: d.depth + 1,
          height: 0,
          parent: d,
          x: d.x,
          y: d.y + 170, // 放在父节点的右侧
          isPlaceholder: true,
          show_child: false,
        }

        d.children = [placeholderNode]
        d.show_child = false
      }
      else {
        d.children = d.data._children
        d.show_child = true
      }
      update(data)
    })

  nodes
    .selectAll('.point-text').text((d: any) => {
      if (d.show_child)
        return '-'
      else
        return d.data.child_num
    }).attr('font-size', (d: any) => {
      if (d.show_child)
        return '12px'
      else
        return '10px'
    })
    .transition(transition)
    .attr('x', (d: any) => d.y + (170 / 2))
    .attr('y', (d: any) => d.x)
}

let svgTransform: d3.ZoomTransform = d3.zoomIdentity
const zoom = d3.zoom().scaleExtent([1, 3]).on('zoom', zoomed)
function zoomed(event: d3.D3ZoomEvent<any, any>) {
  svgTransform = event.transform
  rootNode.attr('transform', event.transform)
  rootLink.attr('transform', event.transform)
  myRootLinks.attr('transform', event.transform)
  svg.select('.node-popover').attr('transform', event.transform as unknown as string)
}

function resetZoom() {
  svg
    .call(zoom.transform, d3.zoomIdentity) // 复位到初始
}

function init() {
  if (!svgEle.value)
    return

  const width = outerContainer.value?.clientWidth
  const height = outerContainer.value?.clientHeight

  svg.on('click', (e: any) => {
    removePopover()
  })
  svg
    .attr('width', width || 0)
    .attr('height', height || 0)

  rootLink = svg.append('g')
    .attr('fill', 'none')
    .attr('stroke', '#165DFF')
    .attr('stroke-opacity', 1)
    .attr('stroke-width', 1)

  myRootLinks = svg.append('g')
    .attr('fill', 'none')
    .attr('stroke', '#165DFF')
    .attr('stroke-opacity', 1)
    .attr('stroke-width', 1)

  rootNode = svg.append('g')

  root.descendants().forEach((d: any, index: number) => {
    d.data.child_num = d.descendants().length - 1
    d.data._children = d.children
    d.id = index
    d.show_child = true
  })

  svg.call(zoom)

  svgEle.value?.appendChild(svg.node()!)
  update(root)
  nextTick(() => {
    fitView()
  })
}

function removePopover() {
  svg.selectAll('.popover')
    .transition()
    .duration(200)
    .style('opacity', 0)
    .remove()

  activeNode = null
  update(root)
}

function expandAllNode() {
  resetZoom()
  expandAll(root)
  removePopover()
}

function expandAll(node: any) {
  if (node.data._children) {
    node.children = node.data._children
    node.show_child = true
  }
  if (node.children)
    node.children.forEach(expandAll)
}

function fitView() {
  const svg = d3.select('#svg-ele')
  const elements = svg.selectAll('.node-item')

  // 初始化边界值
  let minX = Number.POSITIVE_INFINITY; let minY = Number.POSITIVE_INFINITY; let maxX = Number.NEGATIVE_INFINITY; let maxY = Number.NEGATIVE_INFINITY

  // 遍历所有子元素，计算 bbox
  elements.each(function (item: any) {
    const bbox = this.getBBox()

    minX = Math.min(minX, bbox.x)
    minY = Math.min(minY, bbox.y)
    maxX = Math.max(maxX, bbox.x + bbox.width)
    maxY = Math.max(maxY, bbox.y + bbox.height)
  })

  // 增加一点边距
  const padding = 100

  minX -= padding
  minY -= padding
  maxX += padding
  maxY += padding

  // 计算宽高
  const width = maxX - minX
  const height = maxY - minY

  svg.attr('viewBox', `${minX} ${minY} ${width} ${height}`)
}

interface NodeData {
  name: string
  depth: number
  id: number
  children?: NodeData[]
}

// 展开所有节点
function getAllNodes(node: any, result: NodeData[] = []) {
  const nodeData: NodeData = {
    name: node.data.name,
    depth: node.depth,
    id: node.id,
  }

  // 递归遍历所有子节点
  if (node.children) {
    nodeData.children = []
    node.children.forEach((child: any) => getAllNodes(child, nodeData.children))
  }
  result.push(
    nodeData,
  )

  return result
}

// svg 导出 png
async function exportPNG() {
  // 保存当前状态并展开所有节点以获得完整图
  expandAllNode()
  await inlineImages(svgEle.value!)

  const svgElement = svgEle.value?.querySelector('#svg-ele') as SVGElement

  const clonedSvg = svgElement?.cloneNode(true) as SVGElement

  clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg')
  clonedSvg.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink')

  const serializer = new XMLSerializer()
  const svgString = serializer.serializeToString(clonedSvg)

  const blob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' })
  const url = URL.createObjectURL(blob)

  const img = new Image()

  img.crossOrigin = 'anonymous'
  img.onload = () => {
    const scale = 3 // 缩放比例
    const canvas = document.createElement('canvas')

    canvas.width = svgElement.clientWidth * scale
    canvas.height = svgElement.clientHeight * scale

    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D

    ctx.fillStyle = '#ffffff'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    ctx.scale(scale, scale)
    ctx.drawImage(img, 0, 0)

    const a = document.createElement('a')

    a.href = canvas.toDataURL('image/png', 1.0) // 使用最高质量
    a.download = `${t('Network.Tupo.NetworkTopology')}.png`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)

    URL.revokeObjectURL(url)
  }

  img.onerror = e => {
  }

  img.src = url
}

// 导出 PDF
async function exportToPDF() {
  // 保存当前状态并展开所有节点以获得完整图
  expandAllNode()
  await inlineImages(svgEle.value!)

  const svgElement = svgEle.value?.querySelector('#svg-ele') as SVGElement
  if (!svgElement)
    return

  // 避免修改原始 SVG
  const clonedSvg = svgElement.cloneNode(true) as SVGElement

  clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg')
  clonedSvg.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink')

  const svgWidth = svgElement.clientWidth
  const svgHeight = svgElement.clientHeight

  const serializer = new XMLSerializer()
  const svgString = serializer.serializeToString(clonedSvg)

  const blob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' })
  const url = URL.createObjectURL(blob)

  const img = new Image()

  img.crossOrigin = 'anonymous'
  img.onload = () => {
    const scale = 3 // 缩放比例
    const canvas = document.createElement('canvas')

    canvas.width = svgWidth * scale
    canvas.height = svgHeight * scale

    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D

    ctx.fillStyle = '#ffffff'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    ctx.scale(scale, scale)
    ctx.drawImage(img, 0, 0, svgWidth, svgHeight)

    // 定义固定的 PDF 页面宽度（以毫米为单位）1px = 0.264583mm
    const pageWidthMM = canvas.width * 0.264583
    const pageHeightMM = canvas.height * 0.264583

    // 创建指定大小的 PDF
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: [pageWidthMM, pageHeightMM],
    })

    const imgData = canvas.toDataURL('image/png', 1.0)

    pdf.addImage(imgData, 'PNG', 0, 0, pageWidthMM, pageHeightMM)
    pdf.save(`${t('Network.Tupo.NetworkTopology')}.pdf`)

    URL.revokeObjectURL(url)
  }

  img.onerror = e => {
  }

  img.src = url
}

// 导出json
function exportToJSON() {
  expandAllNode()

  const jsonData: any[] = getAllNodes(root)
  const jsonStr = JSON.stringify(jsonData)
  const blob = new Blob([jsonStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)

  const a = document.createElement('a')

  a.href = url
  a.download = `${t('Network.Tupo.NetworkTopology')}_json.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 导出csv
function exportToCSV() {
  expandAllNode()

  const nodeData: any[] = getAllNodes(root)
  function convertToCSV(data: any[], parentName = ''): string {
    let csvRows = ''
    data.forEach(node => {
      const row = `${parentName},${node.name},${node.depth},${node.id}`

      csvRows += `${row}\n`
      if (node.children)
        csvRows += convertToCSV(node.children, node.name)
    })

    return csvRows
  }

  const csvHeader = '父级,名称,层级,ID\n'
  const csvContent = csvHeader + convertToCSV(nodeData)

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')

  a.href = url
  a.download = `${t('Network.Tupo.NetworkTopology')}.csv`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 处理图片加载问题
// 需要将图片转换为 base64 格式
function inlineImages(svgElement: HTMLElement) {
  return new Promise(resolve => {
    const images = svgElement.querySelectorAll('image')
    if (images.length === 0) {
      resolve(svgElement)

      return
    }

    let loadedCount = 0

    const imagePromises = Array.from(images).map(img => {
      return new Promise<void>(resolveImg => {
        const href = img.getAttribute('xlink:href') || img.getAttribute('href')

        // 如果图像已经是 data URL，则跳过
        if (href && href.startsWith('data:')) {
          loadedCount++
          resolveImg()

          return
        }

        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d') as CanvasRenderingContext2D
        const newImg = new Image()

        newImg.crossOrigin = 'anonymous'

        newImg.onload = function () {
          canvas.width = newImg.width
          canvas.height = newImg.height
          ctx.drawImage(newImg, 0, 0)

          // 将图像转换为 base64
          try {
            const dataURL = canvas.toDataURL('image/png')

            // img.setAttribute('xlink:href', dataURL);
            img.setAttribute('href', dataURL)
            loadedCount++
            resolveImg()
          }
          catch (e) {
            resolveImg()
          }
        }

        newImg.onerror = function () {
          resolveImg()
        }

        newImg.src = href
      })
    })

    Promise.all(imagePromises).then(() => resolve(svgElement))
  })
}

const isDialogVisible = ref(false)
const radioGroup = ref(1)

const exportRadio = [
  {
    label: t('Network.Tupo.PNGImage'),
    value: 1,
    icon: 'tabler-photo',
  },
  {
    label: t('Network.Tupo.PDFFile'),
    value: 2,
    icon: 'tabler-file',
  },
  {
    label: t('Network.Tupo.JSONFile'),
    value: 3,
    icon: 'tabler-file-code',
  },
  {
    label: t('Network.Tupo.CSVFile'),
    value: 4,
    icon: 'tabler-file-text',
  },
]

const dialogClosed = () => {
  radioGroup.value = 1
}

const handleExport = () => {
  switch (radioGroup.value) {
    case 1:
      exportPNG()
      break
    case 2:
      exportToPDF()
      break
    case 3:
      exportToJSON()
      break
    case 4:
      exportToCSV()
      break
  }
  isDialogVisible.value = false
}

onMounted(() => {
  getTupoList()
})

onUnmounted(() => {
  // 清理定时器，防止页面切换后定时器继续运行
  if (timer.value) {
    clearTimeout(timer.value)
    timer.value = null
  }
})
</script>

<template>
  <div
    ref="outerContainer"
    class="topology overflow-scroll"
  >
    <VCard class="pa-6 w-100 h-100">
      <div ref="svgEle" />
    </VCard>
    <div class="btn-group">
      <VBtn
        class="ml-4"
        @click="isDialogVisible = true"
      >
        <VIcon
          icon="tabler-file-export"
          start
        />
        {{ t('Network.Tupo.ExportTopology') }}
      </VBtn>
    </div>

    <!-- 导出弹窗 -->
    <VDialog
      v-model="isDialogVisible"
      width="590"
      @after-leave="dialogClosed"
    >
      <DialogCloseBtn @click="isDialogVisible = !isDialogVisible" />
      <VCard class="pa-8">
        <template #title>
          <div class="text-center text-h4 mb-6">
            {{ t('Network.Tupo.ExportAs') }}
          </div>
        </template>
        <div>
          <VRadioGroup v-model="radioGroup">
            <div
              v-for="(item, index) in exportRadio"
              :key="index"
              class="export-radio pa-4 rounded mb-4 border-sm"
              :class="[item.value === radioGroup ? 'border-primary border-opacity-100' : 'border-on-surface border-opacity']"
            >
              <VRadio :value="item.value">
                <template #label>
                  <div
                    class="d-flex align-center font-weight-medium"
                    :class="[item.value === radioGroup ? 'text-primary' : 'text-on-surface opacity-90']"
                  >
                    <VIcon
                      class="mr-1"
                      :icon="item.icon"
                    />
                    {{ item.label }}
                  </div>
                </template>
              </VRadio>
            </div>
          </VRadioGroup>
        </div>
        <div class="d-flex align-center justify-center pa-4">
          <VBtn
            color="primary"
            @click="handleExport"
          >
            {{ t('Network.Tupo.Export') }}
          </VBtn>
          <VBtn
            class="ml-4"
            variant="tonal"
            color="secondary"
            @click="isDialogVisible = false"
          >
            {{ t('Network.Tupo.Cancel') }}
          </VBtn>
        </div>
      </VCard>
    </VDialog>

    <!-- 图例 -->
    <div class="legend-row d-flex align-center">
      <div class="legend-item mr-6 d-flex align-center">
        <div class="line mr-1" />
        <span class="text-on-surface opacity-90">{{ t('Network.Tupo.WiredConnection') }}</span>
      </div>
      <div class="legend-item mr-6 d-flex align-center">
        <div class="dash-line mr-1" />
        <span class="text-on-surface opacity-90">{{ t('Network.Tupo.WirelessConnection') }}</span>
      </div>
      <div class="legend-item mr-6 d-flex align-center">
        <img
          class="2g mr-1"
          :src="icon2G"
        >
        <span class="text-on-surface opacity-90">2.4GHz</span>
      </div>
      <div class="legend-item d-flex align-center">
        <img
          class="5g mr-1"
          :src="icon5G"
        >
        <span class="text-on-surface opacity-90">5GHz</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.topology {
  position: relative;
  block-size: calc(100vh - 172px);

  .btn-group {
    position: absolute;
    inset-block-end: 20px;
    inset-inline-end: 20px;
  }
}
</style>

<style lang="scss">
.topology {
  .legend-row {
    position: absolute;
    inset-block-start: 24px;
    inset-inline-start: 24px;

    .line {
      border-block-start: 2px solid #165dff;
      inline-size: 24px;
    }

    .dash-line {
      border-block-start: 2px dashed #165dff;
      inline-size: 24px;
    }

    img {
      block-size: 22px;
      inline-size: 22px;
    }
  }
}

.node-popover {
  overflow: visible;

  .node-popover-box {
    background: #fff;
    box-shadow:
      0 3px 12px rgba(var(--v-shadow-key-umbra-color), var(--v-shadow-md-opacity)),
      0 0 transparent,
      0 0 transparent;
    inline-size: 175px;

    .item-title {
      color: rgba($color: var(--v-theme-on-surface), $alpha: 90%);
      font-size: 15px;
      font-weight: 500;
    }

    .info-label {
      color: rgba($color: var(--v-theme-on-surface), $alpha: 55%);
      font-size: 12px;
    }

    .info-content {
      color: rgba($color: var(--v-theme-on-surface), $alpha: 90%);
      font-size: 13px;
    }
  }
}
</style>
